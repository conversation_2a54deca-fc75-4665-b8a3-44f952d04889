# AWS Account: veracode-platform-nonprod
# Location: US

image:
  tag: "ATL-3803-eeecc0fd"
secret:
  - name: agent-wrapper-secrets
    type: Opaque
    data:
      keystore-password: "Y2hhbmdlaXQ="
      srcclr-key: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      truststore-password: "dGhlLXFhLXBhc3N3b3Jk"
  - name: agent-wrapper-certs
    type: Opaque
    data:
      keystore.jks: 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
      truststore.jks: /u3+7QAAAAIAAAACAAAAAgAWc2NhLXNjYW5jb25maWctc2VydmljZQAAAYPmdPvNAAVYLjUwOQAABjEwggYtMIIEFaADAgECAgggQXSTBollYTANBgkqhkiG9w0BAQsFADCBpjELMAkGA1UEBhMCVVMxCzAJBgNVBAgMAk1BMRYwFAYDVQQKDA1WZXJhY29kZSBJbmMuMSswKQYDVQQLDCJWZXJhY29kZSBJbmMgQ2VydGlmaWNhdGUgQXV0aG9yaXR5MSQwIgYDVQQDDBtWZXJhY29kZS5xYSBJbnRlcm1lZGlhdGUgQ0ExHzAdBgkqhkiG9w0BCQEWEE5PQ0B2ZXJhY29kZS5jb20wHhcNMjAxMjExMjE0MDM2WhcNMjUxMjEwMjE0MDM2WjBxMQswCQYDVQQGEwJVUzELMAkGA1UECAwCTUExEzARBgNVBAcMCkJ1cmxpbmd0b24xETAPBgNVBAoMCHZlcmFjb2RlMQwwCgYDVQQLDANvcmcxHzAdBgNVBAMMFnNjYS1zY2FuY29uZmlnLXNlcnZpY2UwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDe0hyhchaKAoNlWbPTZiBMW3MtgJ/WtxH+VvgaSPvdDbj+JYI9pziuy9p7MK5aJZJiw8GEB94a1kK4jo6JWgeVzp+vfWVAxL1ngWBPdagCVPobOFuPTfOlvQMxZoiLGhlcMryb6emrk0iZw5iMca5dV9LANH7GUv3LPoZAjmE3ZU+q962bKfha+Zs5AZA8jYMiOG6fvgK3EnGs9ccX7+UDyZ4EgvY7dUvC+RrnVvoNlmcb64ev3JtRA6BUWM0xGx8v6gJvKiCL5v6//UdhNBs3hVdz3lZo9KlBGci7B9PPYpOVFg0p6UANeIoEckBZ1GAOtFa+UH8FGQUsGFaAxzsvAgMBAAGjggGRMIIBjTAJBgNVHRMEAjAAMBEGCWCGSAGG+EIBAQQEAwIGwDA7BglghkgBhvhCAQ0ELhYsT3BlblNTTCBHZW5lcmF0ZWQgQ2xpZW50Ly9TZXJ2ZXIgQ2VydGlmaWNhdGUwHQYDVR0OBBYEFABX7C2nTqu/dhfj6Ff6YiXXFurbMIHhBgNVHSMEgdkwgdaAFJU9a4tNDUG1QnS3zMlY3UgrDSDJoYG5pIG2MIGzMQswCQYDVQQGEwJVUzELMAkGA1UECAwCTUExEzARBgNVBAcMCkJ1cmxpbmd0b24xFjAUBgNVBAoMDVZlcmFjb2RlIEluYy4xKzApBgNVBAsMIlZlcmFjb2RlIEluYyBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkxHDAaBgNVBAMME1ZlcmFjb2RlLnFhIFJvb3QgQ0ExHzAdBgkqhkiG9w0BCQEWEE5PQ0B2ZXJhY29kZS5jb22CAhAAMA4GA1UdDwEB/wQEAwIF4DAdBgNVHSUEFjAUBggrBgEFBQcDAgYIKwYBBQUHAwEwDQYJKoZIhvcNAQELBQADggIBAE8MOK8fsQScBObEVJdBfv1nJCT9MsSWyCEgFuPbhxnl2XH3f4uyzUqIqmtjWUwB8Cty6vmFKKYLBpC8wlJwvMZJXs5cxTLi7iJpJ43XofIDpF0bjSc59kD04lha4PFI6l+Bgsc3fIZDHL8e9moDLqjeZAUCVQfr8GPTffyFw9OxrK+SD7QhYkr+Pbn3N5QCokHD+ZicQduubBU9DVdHPBbIGzl8vZjtcR/UL0wCVMr529yoZig5AC2mCpY2+TLJjM8xm631ehOo7AeAjkQVuVAfqZzVexvIi1auqk2Oq2hRt/Nb/g0ZJojYfWdcQK6e4uGoYwx3+I51dIX7A/CFlOUKGJGusYVrucigI73sgveh17hkENjHHEuY0Yp72FhW7nCHwA5H2Hd4HpFvPa4IxcXSYCkXODXoVmC8pL5EDC5sK/Cn7MEG7JEBFKaweskdr49eKB/iw8FSS7rGCsBk5lctTzgFPpu21FjoUbdIqlaS8s25tPnEQYgwnWkfE98MYVEoz1PJx8zNWoEMtcFcCS1aNBtAo8FyWYcCdkZABxc6wx5ZnleZ/SuQ10cy1CchKZa2tgCy9mNNNbwAt2wdnkEE3coGq+HMmuglEQmMfiETYPRbjLhp8pRJ2YhCs+ljZtK1golne/RZjy1skfuU6Zxx8/B2Gg6e3XKddcIOkx3NAAAAAgAFbXlrZXkAAAFpuqtdDgAFWC41MDkAAAY6MIIGNjCCBB6gAwIBAgICBVswDQYJKoZIhvcNAQELBQAwgaYxCzAJBgNVBAYTAlVTMQswCQYDVQQIDAJNQTEWMBQGA1UECgwNVmVyYWNvZGUgSW5jLjErMCkGA1UECwwiVmVyYWNvZGUgSW5jIENlcnRpZmljYXRlIEF1dGhvcml0eTEkMCIGA1UEAwwbVmVyYWNvZGUucWEgSW50ZXJtZWRpYXRlIENBMR8wHQYJKoZIhvcNAQkBFhBOT0NAdmVyYWNvZGUuY29tMB4XDTE4MTEyNjE2MzAxNFoXDTIzMTEyNTE2MzAxNFowgYAxCzAJBgNVBAYTAlVTMRYwFAYDVQQIEw1NYXNzYWNodXNldHRzMRMwEQYDVQQHEwpCdXJsaW5ndG9uMRYwFAYDVQQKEw1WZXJhY29kZSBJbmMuMQswCQYDVQQLEwJRQTEfMB0GA1UEAxMWc2NhLXNjYW5jb25maWctc2VydmljZTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJnLyLMzH1YyYFOI4wCUPjueqUcYlPyatdCKDboCS2C32rG0kMNNyTYh84gBan0twBQStqL4vzTNFC0vHV/gMD811mPWfqatvHYQ85QFLqX5GLYWJ52Ians+BiLfGQa8o1aKG9N/TVh1cOIFPDO0U8q0Xn4UVlsKsIWgtJRKmt4NwLBJawapddkebhoP++GhpiZmHOUAfEkYOwMNJntrAc+QfQs4Jr7pjcai+mpyPMjyEuzQTBqGzZ6PJxfHQi1u+r7qaFsIfCTXOu9YK89JBxT3/DPxUBymueEeIoYP/u/AAvCNGVeN57TRXPJapyTXubGegLbHIVyutiZhpH3p8T8CAwEAAaOCAZAwggGMMAkGA1UdEwQCMAAwEQYJYIZIAYb4QgEBBAQDAgbAMDoGCWCGSAGG+EIBDQQtFitPcGVuU1NMIEdlbmVyYXRlZCBDbGllbnQvU2VydmVyIENlcnRpZmljYXRlMB0GA1UdDgQWBBS1Rn1OY2mX4pXSlKiZdwPTqgT8FjCB4QYDVR0jBIHZMIHWgBSVPWuLTQ1BtUJ0t8zJWN1IKw0gyaGBuaSBtjCBszELMAkGA1UEBhMCVVMxCzAJBgNVBAgMAk1BMRMwEQYDVQQHDApCdXJsaW5ndG9uMRYwFAYDVQQKDA1WZXJhY29kZSBJbmMuMSswKQYDVQQLDCJWZXJhY29kZSBJbmMgQ2VydGlmaWNhdGUgQXV0aG9yaXR5MRwwGgYDVQQDDBNWZXJhY29kZS5xYSBSb290IENBMR8wHQYJKoZIhvcNAQkBFhBOT0NAdmVyYWNvZGUuY29tggIQADAOBgNVHQ8BAf8EBAMCBeAwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMA0GCSqGSIb3DQEBCwUAA4ICAQCBaHa0z1rwaU5/knGkrhkBrFWTAd+Xbojl6p2j1V6KRVI7+ubXqyN697j5mIN+mH/YCQp2YyuFQsg6rapb8TXIRzkIZ9tC3WSYUJOxaGLI0F/lKilbXc9rz8JSOpR19Gcd8OeYNs9o0dmxCrzncNKktFRthQEIlO7/XUqftoBtCdwLCK+tLjVKPu/T7yrpO4kI577MZarrVVgvi9rIWGfEIP5g4L48S837SbMf63jnlz/Y+gqSUQ8HNMnp+ooQudazbMhIYT98rBmD4La+uGTruKbB8KGDssm5cQKd6lJxXUeYu3J1FlEay6pVucyxC8S6Tv4dyvI57Lez2pLkpFH9EYKMK3iAU6RrBHt9NfQmu8Dzbh3AMPJ6Y9A0h1B/s6XWXDshvs4WdZu9p7J3JTySnyJT4l8s7kSh5iGw+4TdN3q3vuGWBmCjsdosvaIYurwbTl3FX3Ow3OMb8JTMNCR1qB9HKeo+ajUbBn95JMXY+buiVhtxYefXjEyV35gT6N8UUve+K74ibvjUw9ZfPiKbhGtozX80V4iMMy1PE0TvdX7AEFxayiSqqtVt6ESz7ShxZI6xAJe6WaNjXvF0I5HvyGTIwyQkDPC2AXLyumI0m66yy0e2Kkl0IcBGslnkowq8onEe8AZ/m40LPOgMApFtmGLZwGIOugj/p+NN2Wo1X+yfslB8O0RSsiXPDcG4Zhs9Mbc0

env:
  ROLE_ARN: "arn:aws:iam::************:role/vosp-S3-role-stage"
  ROLE_SESSION_NAME: "agent-wrapper-session-stage"
  SQS_QUEUE: "stage-upload-scan-config"
  SRCCLR_BUCKET: "agent-wrapper-evidence-stage"
  SRCCLR_QUEUE: "stage-upload-scan-evidence"

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/agent-wrapper-stage-dyn-tachyon-serviceaccount