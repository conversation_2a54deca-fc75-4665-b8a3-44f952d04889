app: agent-wrapper
nameOverride: agent-wrapper
fullnameOverride: agent-wrapper
image:
  registry: "205744758777.dkr.ecr.us-east-1.amazonaws.com"
  name: "veracode/sca-agent-wrapper"
  tag: "1576586"
annotations:
  hpa.autoscaling.banzaicloud.io/minReplicas: "1"
  hpa.autoscaling.banzaicloud.io/maxReplicas: "1"
  cpu.hpa.autoscaling.banzaicloud.io/targetAverageUtilization: "60"
replicaCount: 1
ports: |
  - containerPort: 8080
    name: management
    protocol: TCP
  - containerPort: 8888
    name: jmx
    protocol: TCP

podLabels:
  # allow datadog to inject variables required for APM
  admission.datadoghq.com/enabled: "true"

env:
  DD_PROFILING_ENABLED: "__DD_PROFILING_ENABLED__"
  DD_SERVICE: "agent-wrapper"
  DD_TAGS: "env:__ENV_NAME__ stack_name:__ENV_<PERSON>ANCH__"
  JAVA_OPTS: "-Xms4G -Xmx4G -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.port=8888 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.local.only=false -Djava.rmi.server.hostname=localhost -Djava.security.egd=file:/dev/urandom -XX:+ExitOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heap-dump.hprof"
  JAVA_TOOL_OPTIONS: "-javaagent:/datadog/dd-java-agent.jar"
  LOGGING_LEVEL_COM_DATADOG_PROFILING_UPLOADER_PROFILEUPLOADER: "FATAL"
  LOGGING_LEVEL_DATADOG_TRACE_AGENT_COMMON_WRITER_DDAGENT_DDAGENTAPI: "FATAL"
  ROLE_ARN: "arn:aws:iam::135394645105:role/vosp-S3-role-dev"
  ROLE_SESSION_NAME: "agent-wrapper-session-dev"
  SCAN_TIMEOUT: "30m"
  SCANCONFIG_KEYSTOREPATH: "/certs/keystore/keystore.jks"
  SCANCONFIG_TRUSTSTOREPATH: "/certs/keystore/truststore.jks"
  SCANCONFIG_URL: "https://sca-scanconfig-service"
  SPRING_PROFILES_ACTIVE: "qa"
  SQS_QUEUE: "dev-upload-scan-config"
  SRCCLR_BUCKET: "agent-wrapper-evidence-dev"
  SRCCLR_ISHTTP: "false"
  SRCCLR_QUEUE: "dev-upload-scan-evidence"
  SRCCLR_URL: "http://platform-backend"
  UNPACK_RECURSIVELY: "true"

valueFrom:
  - name: SCANCONFIG_KEYSTOREPASSWORD
    valueFrom:
      secretKeyRef:
        name: agent-wrapper-secrets
        key: keystore-password
  - name: SCANCONFIG_TRUSTSTOREPASSWORD
    valueFrom:
      secretKeyRef:
        name: agent-wrapper-secrets
        key: truststore-password
  - name: SRCCLR_KEY
    valueFrom:
      secretKeyRef:
        name: agent-wrapper-secrets
        key: srcclr-key

readinessProbe: |
  failureThreshold: 3
  httpGet:
    path: "/actuator/health"
    port: 8080
    scheme: HTTP
  initialDelaySeconds: 30
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1
resources:
  limits:
    memory: 6G
  requests:
    memory: 4G
volumeMounts:
  - name: agent-wrapper-secrets
    mountPath: "/secrets"
    readOnly: true
  - name: agent-wrapper-certs
    mountPath: "/certs/keystore"
    readOnly: true
  - name: agent-wrapper-tmp
    mountPath: "/tmp"

volumes:
  - name: agent-wrapper-secrets
    secret:
      secretName: agent-wrapper-secrets
      defaultMode: 420
  - name: agent-wrapper-certs
    secret:
      secretName: agent-wrapper-certs
      defaultMode: 420
  # Note that these volumes are shared with containers in the pod
  - name: agent-wrapper-tmp
    ephemeral:
      volumeClaimTemplate:
        metadata:
          labels:
            type: agent-wrapper-tmp-volume
        spec:
          accessModes: ["ReadWriteOnce"]
          storageClassName: gp3-retain
          resources:
            requests:
              storage: 5Gi

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/agent-wrapper-dev-dyn-tachyon-serviceaccount
  name: agent-wrapper

#chart level values
imageConfig:
  pullPolicy: IfNotPresent

initContainerConfig: {}

imagePullSecrets: []
hostname: ""

service:
  type: ClusterIP
  port: 80

automountServiceAccountToken: true

ingress:
  enabled: false
  annotations: {}
  hosts:
    - host: chart-example.local
      paths: []

  tls: []

alb_ingress:
  enabled: false

virtualService:
  enabled: false

internalIngress: {}
nodeSelector: {}

tolerations: []

affinity: {}

cronjob:
  enabled: false

job:
  enabled: false

## Allow definition of pvc
persistence:
  ##
  ## enable persistance storage
  enabled: false

  ## Persistent Volume Storage Class
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  # storageClass: default
  accessMode: ReadWriteOnce
  ##
  ## Persistant storage size request
  size: 1Gi