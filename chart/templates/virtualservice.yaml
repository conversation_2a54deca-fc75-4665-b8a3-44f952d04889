{{- if .Values.virtualService.enabled -}}
{{- $fullName := include "agent-wrapper.fullname" . -}}
{{- $targetPort := .Values.service.targetPort | default "http" }} # if no targetPort is defined, it defaults to http
{{- $serviceName := .Values.service.name | default $fullName }} 
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ $fullName }}
spec:
  hosts:
  {{- with .Values.virtualService.hosts }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- if .Values.virtualService.gateways }}
  gateways:
  {{- toYaml .Values.virtualService.gateways | nindent 4 }}
  {{- end }}
  {{- if .Values.virtualService.httpRoutes }}
  http:
  {{- toYaml .Values.virtualService.httpRoutes | nindent 4 }}
  {{- end }}
  {{- if .Values.virtualService.tlsRoutes }}
  tls:
  {{- toYaml .Values.virtualService.tlsRoutes | nindent 4 }}
  {{- end }}
  {{- if .Values.virtualService.tcpRoutes }}
  tcp:
  {{- toYaml .Values.virtualService.tcpRoutes | nindent 4 }}
  {{- end }}
{{- end -}}