{{- if .Values.csi_secret }}
  {{- $en := include "agent-wrapper.fullname" . -}}
  {{- $esName := .Values.csi_secret.name | default $en}}
apiVersion: secrets-store.csi.x-k8s.io/v1alpha1
kind: SecretProviderClass
metadata:
  name: {{ $esName }}
spec:
 # Hardcoded to aws unless we plan to use other providers.
  provider: aws
  {{- with .Values.csi_secret.parameters }} # will define pod volumes, if defined in the values file.
  parameters:
    objects: |
    {{- toYaml . | nindent 8 }}
  {{- end }}
  {{- with .Values.csi_secret.syncSecrets }} # will define pod volumes, if defined in the values file.
  secretObjects:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}