{{ if .Values.storage_class }}
  {{- $en := include "agent-wrapper.fullname" . -}}
  {{- $secretName := .Values.storage_class.name | default $en}}
kind: StorageClass
apiVersion: storage.k8s.io/v1
metadata:
  name: {{ default $secretName .name }}
  labels:
    {{- with .Values.storage_class.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- if .annotations }}
  annotations:
    {{- toYaml .annotations | nindent 4 }}
  {{- end }}
provisioner: {{ .Values.storage_class.provisioner }}
volumeBindingMode: {{ .Values.storage_class.volumeBindingMode }}
reclaimPolicy: {{ .Values.storage_class.reclaimPolicy }}
  {{ if .Values.storage_class.allowVolumeExpansion }}
allowVolumeExpansion: {{ .Values.storage_class.allowVolumeExpansion }}
  {{ end }}
  {{- with .Values.storage_class.parameters }}
parameters:
  {{- toYaml . | nindent 2 }}
  {{- end }}
  {{- with .Values.storage_class.mountOptions }}
mountOptions:
  {{- toYaml . | nindent 2 }}
  {{- end }}
{{ end -}}