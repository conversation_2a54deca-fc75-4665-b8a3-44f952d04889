{{- if .Values.external_secret }}
  {{- $en := include "agent-wrapper.fullname" . -}}
  {{- $esName := .Values.external_secret.name | default $en}}
apiVersion: 'kubernetes-client.io/v1'
kind: ExternalSecret
metadata:
  name: {{ $esName }}
  {{- with .Values.external_secret.annotations }}
annotations:
  {{- toYaml . | nindent 4 }} # Adds the annotations
  {{- end }}
spec:
  backendType: secretsManager
  {{- with .Values.external_secret.roleArn }}
  roleArn: {{ . }}
  {{- end }}
  data:
    {{- with .Values.external_secret.data }}
      {{- toYaml . | nindent 4 }}
      {{- end }}
  {{- end }}
  {{- if .Values.extraExternalSecrets }}
  {{- range .Values.extraExternalSecrets }} # iterates through the list of externalsecrets defined in the values file
---
apiVersion: 'kubernetes-client.io/v1'
kind: ExternalSecret
metadata:
  name: {{ .name }}
  {{- if .annotations }}
  annotations:
    {{- toYaml .annotations | nindent 4 }}
  {{- end }}
spec:
  backendType: secretsManager
  data:  {{- range .data }} # iterates through all the defined entries of the current externalsecret
    - key: {{ .key }}
      name: {{ .name }}
      property: {{ .property }}
  {{- end }}
  {{- end }}
  {{- end }}

