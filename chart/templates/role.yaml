{{- if .Values.rbac }} #defines Role and RoleBinding for the current application
{{- $cn := include "agent-wrapper.fullname" . -}}
{{- $fullName := .Values.rbac.name | default $cn}}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ $fullName }}
  labels:
{{ include "agent-wrapper.labels" . | indent 4 }}
{{- with .Values.rbac.rules }}
rules:
  {{- toYaml . | nindent 2 }}
{{- end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ $fullName }}  
  labels:
{{ include "agent-wrapper.labels" . | indent 4 }}    
subjects:
  - kind: ServiceAccount
    name: {{ $fullName }}
    namespace: {{ .Release.Namespace }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ $fullName }} 
{{- end }}
